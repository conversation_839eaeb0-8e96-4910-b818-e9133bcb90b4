<?php
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/auth.php';
if (session_status() !== PHP_SESSION_ACTIVE) { session_start(); }
if (empty($_SESSION['csrf'])) { $_SESSION['csrf'] = bin2hex(random_bytes(16)); }

function sanitize_upload_name(string $name): string {
    $base = pathinfo($name, PATHINFO_BASENAME);
    $san = preg_replace('/[^A-Za-z0-9._\-\s]/u', '_', $base);
    $san = ltrim($san, '.');
    return $san === '' ? ('file_' . date('Ymd_His') . '.pptx') : $san;
}

function is_valid_ppt_mime(string $tmp): bool {
    $fi = finfo_open(FILEINFO_MIME_TYPE);
    $mime = $fi ? finfo_file($fi, $tmp) : '';
    if ($fi) { finfo_close($fi); }
    return in_array($mime, ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'], true);
}

$errors = [];
$success = '';
$confirmOverwrite = false;
$pendingName = '';

/* تم نقل parseFilenameMeta إلى db.php لاستخدامه كمساعد مشترك */

if (isset($_GET['logout'])) { admin_logout(); $success = 'تم تسجيل الخروج.'; }

$handledAuthPost = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['admin_login'])) {
    $csrfOk = isset($_POST['csrf_token']) && hash_equals($_SESSION['csrf'] ?? '', $_POST['csrf_token']);
    if (!$csrfOk) {
        $errors[] = 'طلب غير صالح: رمز CSRF مفقود أو غير صحيح.';
    } else {
        $pwd = $_POST['password'] ?? '';
        if (verify_admin_password($pwd)) { admin_login(); $success = 'تم تسجيل الدخول.'; } else { $errors[] = 'كلمة المرور غير صحيحة.'; }
    }
    $handledAuthPost = true;
}
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['admin_setup'])) {
    $csrfOk = isset($_POST['csrf_token']) && hash_equals($_SESSION['csrf'] ?? '', $_POST['csrf_token']);
    if (!$csrfOk) {
        $errors[] = 'طلب غير صالح: رمز CSRF مفقود أو غير صحيح.';
    } else {
        $p1 = trim($_POST['password'] ?? '');
        $p2 = trim($_POST['password_confirm'] ?? '');
        if ($p1 === '' || !hash_equals($p1, $p2)) { $errors[] = 'كلمتا المرور غير متطابقتين.'; }
        else { if (set_admin_password($p1)) { $success = 'تم تعيين كلمة المرور. الرجاء تسجيل الدخول.'; } else { $errors[] = 'تعذر حفظ كلمة المرور.'; } }
    }
    $handledAuthPost = true;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$handledAuthPost && admin_logged_in()) {
    $csrfOk = isset($_POST['csrf_token']) && hash_equals($_SESSION['csrf'] ?? '', $_POST['csrf_token']);
    if (!$csrfOk) {
        $errors[] = 'طلب غير صالح: رمز CSRF مفقود أو غير صحيح.';
    } else {
    // حذف
    if (isset($_POST['delete_id'])) {
        $id = (int)$_POST['delete_id'];
        $row = findLessonById($id);
        if ($row) {
            $path = __DIR__ . '/uploads/' . $row['stored_name'];
            if (is_file($path)) { @unlink($path); }
            deleteLessonById($id);
            $success = 'تم حذف الملف بنجاح.';
        } else {
            $errors[] = 'السجل غير موجود.';
        }
    // إعادة التحميل
    } elseif (isset($_POST['reupload_id'])) {
        $id = (int)$_POST['reupload_id'];
        $row = findLessonById($id);
        if (!$row) {
            $errors[] = 'السجل غير موجود.';
        } elseif (!isset($_FILES['reupload_file']) || $_FILES['reupload_file']['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'يرجى اختيار ملف لإعادة الرفع.';
        } else {
            $file = $_FILES['reupload_file'];
            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($ext, ['ppt', 'pptx'], true)) {
                $errors[] = 'يسمح فقط بملفات PPT أو PPTX';
            } else {
                if ($file['size'] > 104857600) { $errors[] = 'يتجاوز الملف الحد المسموح (100MB).'; }
                if (!is_valid_ppt_mime($file['tmp_name'])) { $errors[] = 'نوع الملف غير صالح بحسب MIME.'; }
                $target = __DIR__ . '/uploads/' . $row['stored_name'];
                if (is_file($target)) { @unlink($target); }
                if (!move_uploaded_file($file['tmp_name'], $target)) {
                    $errors[] = 'فشل استبدال الملف.';
                } else {
                    // تحديث الحجم وتاريخ الرفع مع الإبقاء على البيانات الأخرى
                    $data = [
                        'filename' => $row['filename'],
                        'stored_name' => $row['stored_name'],
                        'subject' => 'AR',
                        'level' => (int)$row['level'],
                        'track' => (int)$row['track'],
                        'stage' => (int)$row['stage'],
                        'week' => (int)$row['week'],
                        'branch' => $row['branch'],
                        'size' => is_file($target) ? filesize($target) : (int)$row['size'],
                    ];
                    updateLessonById($id, $data);
                    $success = 'تم إعادة رفع الملف بنجاح.';
                }
            }
        }
    // فرع تأكيد الاستبدال
    } elseif (isset($_POST['overwrite'])) {
        $overwrite = $_POST['overwrite'] === '1';
        $pending = $_SESSION['pending_upload'] ?? null;
        if (!$pending) {
            $errors[] = 'لا توجد عملية مؤقتة للاستبدال.';
        } else {
            $uploadDir = __DIR__ . '/uploads';
            $tmpDir = __DIR__ . '/uploads/tmp';
            $tmpPath = $tmpDir . '/' . $pending['tmp'];
            $finalPath = $uploadDir . '/' . $pending['orig'];
            if ($overwrite) {
                // حذف الملف القديم إن وُجد ثم نقل المؤقت للاسم النهائي
                if (is_file($finalPath)) { @unlink($finalPath); }
                if (!@rename($tmpPath, $finalPath)) {
                    $errors[] = 'فشل استبدال الملف القديم.';
                } else {
                    // تحديث/إدراج السجل
                    $existing = findLessonByFilename($pending['orig']);
                    $data = [
                        'filename' => $pending['orig'],
                        'stored_name' => $pending['orig'],
                        'subject' => 'AR',
                        'level' => (int)$pending['level'],
                        'track' => (int)$pending['track'],
                        'stage' => (int)$pending['stage'],
                        'week' => (int)$pending['week'],
                        'branch' => $pending['branch'],
                        'size' => (int)($pending['size'] ?? (is_file($finalPath) ? filesize($finalPath) : 0)),
                    ];
                    if ($existing) {
                        updateLessonById((int)$existing['id'], $data);
                        $success = 'تم استبدال الملف وتحديث البيانات بنجاح.';
                    } else {
                        insertLesson($data);
                        $success = 'تم حفظ الملف الجديد بنجاح.';
                    }
                }
                // تنظيف الجلسة
                unset($_SESSION['pending_upload']);
            } else {
                // إلغاء: حذف المؤقت وتنظيف
                if (is_file($tmpPath)) { @unlink($tmpPath); }
                unset($_SESSION['pending_upload']);
                $success = 'تم إلغاء العملية.';
            }
        }
    } elseif (isset($_POST['import_from_folder'])) {
        // استيراد الملفات الموجودة في مجلد الرفع
        $uploadDir = __DIR__ . '/uploads';
        if (!is_dir($uploadDir)) { @mkdir($uploadDir, 0777, true); }
        $entries = @scandir($uploadDir) ?: [];
        $inserted = 0; $skipped = 0; $invalid = 0;
        foreach ($entries as $fn) {
            if ($fn === '.' || $fn === '..' || $fn === 'tmp') { continue; }
            $path = $uploadDir . '/' . $fn;
            if (!is_file($path)) { continue; }
            $ext = strtolower(pathinfo($fn, PATHINFO_EXTENSION));
            if (!in_array($ext, ['ppt','pptx'], true)) { $invalid++; continue; }
            // تحقق من وجوده في قاعدة البيانات
            if (findLessonByFilename($fn)) { $skipped++; continue; }
            // استخراج القيم من الاسم
            $meta = parseFilenameMeta($fn);
            $level = isset($meta['level']) ? (int)$meta['level'] : 1;
            $track = isset($meta['track']) ? (int)$meta['track'] : 1;
            $stage = isset($meta['stage']) ? (int)$meta['stage'] : 1;
            $week = isset($meta['week']) ? (int)$meta['week'] : 1;
            $branch = isset($meta['branch']) ? $meta['branch'] : 'center';
            $size = filesize($path) ?: 0;
            insertLesson([
                'filename' => $fn,
                'stored_name' => $fn,
                'subject' => 'AR',
                'level' => $level,
                'track' => $track,
                'stage' => $stage,
                'week' => $week,
                'branch' => $branch,
                'size' => $size,
            ]);
            $inserted++;
        }
        if ($inserted === 0 && $skipped === 0) {
            $errors[] = 'لا توجد ملفات جديدة للاستيراد.';
        } else {
            $success = 'تم استيراد ' . $inserted . ' ملف/ملفات. تم تخطّي ' . $skipped . ' ملفات موجودة.';
        }
    } else {
        // فرع الرفع العادي
        $level = (int)($_POST['level'] ?? 1);
        $track = (int)($_POST['track'] ?? 1);
        $stage = (int)($_POST['stage'] ?? 1);
        $week  = (int)($_POST['week'] ?? 1);
        $branch = $_POST['branch'] ?? 'center';

        if (!in_array($level, levels(), true)) $errors[] = 'قيمة المستوى غير صحيحة';
        if (!in_array($track, tracks(), true)) $errors[] = 'قيمة المسار غير صحيحة';
        if (!in_array($stage, stages(), true)) $errors[] = 'قيمة المرحلة غير صحيحة';
        if (!in_array($week, weeks(), true)) $errors[] = 'قيمة الأسبوع غير صحيحة';
        $branchCodes = array_map(fn($b) => $b['code'], branches());
        if (!in_array($branch, $branchCodes, true)) $errors[] = 'قيمة الفرع غير صحيحة';

        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'يجب اختيار ملف للرفع';
        } else {
            $file = $_FILES['file'];
            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($ext, ['ppt', 'pptx'], true)) {
                $errors[] = 'يسمح فقط بملفات PPT أو PPTX';
            }
            if ($file['size'] > 104857600) { $errors[] = 'يتجاوز الملف الحد المسموح (100MB).'; }
            if (!is_valid_ppt_mime($file['tmp_name'])) { $errors[] = 'نوع الملف غير صالح بحسب MIME.'; }
        }

        if (!$errors) {
            $uploadDir = __DIR__ . '/uploads';
            $tmpDir = __DIR__ . '/uploads/tmp';
            if (!is_dir($uploadDir)) { mkdir($uploadDir, 0777, true); }
            if (!is_dir($tmpDir)) { mkdir($tmpDir, 0777, true); }

            $originalName = sanitize_upload_name($file['name']);
            // استخراج القيم من اسم الملف وفرضها على القيم المدخلة
            $meta = parseFilenameMeta($originalName);
            if (isset($meta['branch'])) { $branch = $meta['branch']; }
            if (isset($meta['track']))  { $track  = $meta['track']; }
            if (isset($meta['stage']))  { $stage  = $meta['stage']; }
            if (isset($meta['week']))   { $week   = $meta['week']; }
            $finalPath = $uploadDir . '/' . $originalName;

            // تحقق من التكرار
            $existsDisk = is_file($finalPath);
            $existsDb = (bool)findLessonByFilename($originalName);

            if ($existsDisk || $existsDb) {
                // نقل الملف مؤقتاً ثم طلب التأكيد
                $tempName = uniqid('tmp_', true) . '.' . $ext;
                $tempPath = $tmpDir . '/' . $tempName;
                if (!move_uploaded_file($file['tmp_name'], $tempPath)) {
                    $errors[] = 'فشل حفظ الملف المؤقت للتأكيد.';
                } else {
                    $_SESSION['pending_upload'] = [
                        'tmp' => $tempName,
                        'orig' => $originalName,
                        'level' => $level,
                        'track' => $track,
                        'stage' => $stage,
                        'week' => $week,
                        'branch' => $branch,
                        'size' => $file['size'],
                    ];
                    $confirmOverwrite = true;
                    $pendingName = $originalName;
                }
            } else {
                // لا يوجد تكرار: حفظ مباشر
                if (!move_uploaded_file($file['tmp_name'], $finalPath)) {
                    $errors[] = 'فشل نقل الملف إلى مجلد الرفع';
                } else {
                    $size = filesize($finalPath) ?: 0;
                    insertLesson([
                        'filename' => $originalName,
                        'stored_name' => $originalName,
                        'subject' => 'AR',
                        'size' => $size,
                        'level' => $level,
                        'track' => $track,
                        'stage' => $stage,
                        'week' => $week,
                        'branch' => $branch,
                    ]);
                    $success = 'تم رفع الملف وإضافته بنجاح';
                }
            }
        }
    }
    }
}
// حساب فلاتر العرض أسفل النموذج
$filterLevel = (int)($_POST['level'] ?? $_GET['level'] ?? 1);
$filterTrack = (int)($_POST['track'] ?? $_GET['track'] ?? 1);
$filterStage = (int)($_POST['stage'] ?? $_GET['stage'] ?? 1);
$filterWeek  = (int)($_POST['week'] ?? $_GET['week'] ?? 1);
$filterBranch = $_POST['branch'] ?? $_GET['branch'] ?? 'center';
$filtersForList = [
  'level' => $filterLevel,
  'track' => $filterTrack,
  'stage' => $filterStage,
  'week' => $filterWeek,
  'branch' => $filterBranch,
];
$lessons = findLessons($filtersForList, '');
$branchNames = [];
foreach (branches() as $b) { $branchNames[$b['code']] = $b['name']; }
?>
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>إدارة التطبيق - رفع الملفات</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body class="container py-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h4 m-0">إدارة التطبيق</h1>
    <div class="d-flex gap-2">
      <a class="btn btn-outline-secondary" href="index.php">العودة للواجهة</a>
      <?php if (admin_logged_in()): ?>
      <a class="btn btn-outline-danger" href="?logout=1">تسجيل الخروج</a>
      <?php endif; ?>
      <form method="post">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
        <input type="hidden" name="import_from_folder" value="1">
        <button class="btn btn-success" type="submit">استيراد من المجلد</button>
      </form>
    </div>
  </div>

  <?php if ($errors): ?>
    <div class="alert alert-danger">
      <ul class="m-0">
        <?php foreach ($errors as $e): ?><li><?= htmlspecialchars($e) ?></li><?php endforeach; ?>
      </ul>
    </div>
  <?php elseif ($success): ?>
    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
  <?php endif; ?>

  <?php if ($confirmOverwrite): ?>
    <div class="alert alert-warning d-flex justify-content-between align-items-center">
      <div>يوجد ملف باسم <strong><?= htmlspecialchars($pendingName) ?></strong>. هل تريد استبداله بالملف الجديد؟</div>
      <div class="d-flex gap-2">
        <form method="post">
          <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
          <input type="hidden" name="overwrite" value="1">
          <button class="btn btn-danger" type="submit">استبدال</button>
        </form>
        <form method="post">
          <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
          <input type="hidden" name="overwrite" value="0">
          <button class="btn btn-secondary" type="submit">إلغاء</button>
        </form>
      </div>
    </div>
  <?php endif; ?>

  <?php if (!admin_logged_in()): ?>
    <div class="card shadow-sm mb-4">
      <div class="card-body">
        <?php $prov = admin_password_provider(); ?>
        <?php if (($prov['type'] ?? 'none') === 'none'): ?>
          <h2 class="h6 mb-3">إعداد كلمة مرور المدير</h2>
          <form method="post" class="row g-3">
            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
            <input type="hidden" name="admin_setup" value="1">
            <div class="col-12 col-md-6">
              <label class="form-label">كلمة المرور</label>
              <input type="password" name="password" class="form-control" required>
            </div>
            <div class="col-12 col-md-6">
              <label class="form-label">تأكيد كلمة المرور</label>
              <input type="password" name="password_confirm" class="form-control" required>
            </div>
            <div class="col-12">
              <button class="btn btn-primary" type="submit">حفظ</button>
            </div>
          </form>
        <?php else: ?>
          <h2 class="h6 mb-3">تسجيل الدخول للمدير</h2>
          <form method="post" class="row g-3">
            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
            <input type="hidden" name="admin_login" value="1">
            <div class="col-12 col-md-6">
              <label class="form-label">كلمة المرور</label>
              <input type="password" name="password" class="form-control" required>
            </div>
            <div class="col-12">
              <button class="btn btn-primary" type="submit">دخول</button>
            </div>
          </form>
        <?php endif; ?>
      </div>
    </div>
  <?php else: ?>

  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <form method="post" enctype="multipart/form-data" class="row g-3">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
        <div class="col-12">
          <label class="form-label">الملف (PPT/PPTX)</label>
          <input type="file" name="file" accept=".ppt,.pptx" class="form-control" required>
        </div>
        <div class="col-6 col-md-3">
          <label class="form-label">المستوى</label>
          <select name="level" class="form-select">
            <?php foreach (levels() as $lv): ?>
              <option value="<?= $lv ?>"><?= $lv ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-6 col-md-3">
          <label class="form-label">المسار</label>
          <select name="track" class="form-select">
            <?php foreach (tracks() as $tr): ?>
              <option value="<?= $tr ?>"><?= $tr ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-6 col-md-3">
          <label class="form-label">المرحلة</label>
          <select name="stage" class="form-select">
            <?php foreach (stages() as $st): ?>
              <option value="<?= $st ?>"><?= $st ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-6 col-md-3">
          <label class="form-label">الأسبوع</label>
          <select name="week" class="form-select">
            <?php foreach (weeks() as $wk): ?>
              <option value="<?= $wk ?>"><?= $wk ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-12 col-md-4">
          <label class="form-label">الفرع</label>
          <select name="branch" class="form-select">
            <?php foreach (branches() as $br): ?>
              <option value="<?= htmlspecialchars($br['code']) ?>"><?= htmlspecialchars($br['name']) ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-12">
          <button class="btn btn-primary" type="submit">رفع الملف</button>
        </div>
      </form>
    </div>
  </div>

  <!-- القائمة أسفل إطار التصفية -->
  <div class="card">
    <div class="card-body">
      <h2 class="h5 mb-3">الملفات المطابقة للفلاتر الحالية</h2>
      <?php if (!$lessons): ?>
        <div class="alert alert-light border">لا توجد ملفات</div>
      <?php else: ?>
        <div class="table-responsive">
          <table class="table align-middle">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>الفرع</th>
                <th>المسار</th>
                <th>المرحلة</th>
                <th>الأسبوع</th>
                <th>الحجم</th>
                <th class="text-end">إجراءات</th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($lessons as $row): ?>
                <tr>
                  <td><?= htmlspecialchars($row['filename']) ?></td>
                  <td><?= htmlspecialchars($branchNames[$row['branch']] ?? $row['branch']) ?></td>
                  <td><?= (int)$row['track'] ?></td>
                  <td><?= (int)$row['stage'] ?></td>
                  <td><?= (int)$row['week'] ?></td>
                  <td><?= htmlspecialchars(humanSize((int)$row['size'])) ?></td>
                  <td class="text-end">
                    <div class="d-inline-flex gap-2">
                      <form method="post" onsubmit="return confirm('هل أنت متأكد من الحذف؟');">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
                        <input type="hidden" name="delete_id" value="<?= (int)$row['id'] ?>">
                        <button class="btn btn-outline-danger btn-sm" type="submit">حذف</button>
                      </form>
                      <form method="post" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
                        <input type="hidden" name="reupload_id" value="<?= (int)$row['id'] ?>">
                        <input type="file" name="reupload_file" accept=".ppt,.pptx" class="form-control form-control-sm" style="max-width:220px" required>
                        <button class="btn btn-outline-primary btn-sm" type="submit">إعادة التحميل</button>
                      </form>
                      </div>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>
  <?php endif; ?>
</body>
</html>
