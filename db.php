<?php
// db.php - SQLite database and uploads directory setup

function ensureDataDirs(): void {
    $baseDir = __DIR__;
    $dataDir = $baseDir . DIRECTORY_SEPARATOR . 'data';
    $uploadsDir = $baseDir . DIRECTORY_SEPARATOR . 'uploads';
    if (!is_dir($dataDir)) { @mkdir($dataDir, 0777, true); }
    if (!is_dir($uploadsDir)) { @mkdir($uploadsDir, 0777, true); }
}

function db(): PDO {
    ensureDataDirs();
    $dbPath = __DIR__ . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'database.sqlite';
    $init = !file_exists($dbPath);
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    if ($init) {
        $pdo->exec('CREATE TABLE IF NOT EXISTS lessons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            stored_name TEXT NOT NULL,
            subject TEXT NOT NULL,
            level INTEGER NOT NULL,
            track INTEGER NOT NULL,
            stage INTEGER NOT NULL,
            week INTEGER NOT NULL,
            branch TEXT NOT NULL,
            size INTEGER NOT NULL,
            uploaded_at TEXT NOT NULL
        );');
        $pdo->exec('CREATE INDEX IF NOT EXISTS idx_filters ON lessons(subject, level, track, stage, week);');
        // إضافة فهرس خاص بالفرع
        $pdo->exec('CREATE INDEX IF NOT EXISTS idx_branch_filters ON lessons(branch, level, track, stage, week);');
    }
    // ضمان وجود عمود "branch" عند قواعد بيانات سابقة
    $cols = $pdo->query('PRAGMA table_info(lessons)')->fetchAll(PDO::FETCH_ASSOC);
    $hasBranch = false;
    foreach ($cols as $col) { if (($col['name'] ?? '') === 'branch') { $hasBranch = true; break; } }
    if (!$hasBranch) {
        $pdo->exec('ALTER TABLE lessons ADD COLUMN branch TEXT NOT NULL DEFAULT "center";');
        $pdo->exec('CREATE INDEX IF NOT EXISTS idx_branch_filters ON lessons(branch, level, track, stage, week);');
    }
    return $pdo;
}

function subjects(): array {
    // Customize subjects here
    return [
        ['code' => 'AR', 'name' => 'اللغة الأمازيغية'],
        ['code' => 'FR', 'name' => 'اللغة الفرنسية'],
        ['code' => 'AM', 'name' => 'الأمازيغية (تيفيناغ)']
    ];
}

function weeks(): array { return range(1, 6); }
function levels(): array { return range(1, 6); }
function tracks(): array { return range(1, 3); }
function stages(): array { return range(1, 5); }

// الفروع
function branches(): array {
    return [
        ['code' => 'north', 'name' => 'الشمال'],
        ['code' => 'center', 'name' => 'الوسط'],
        ['code' => 'south', 'name' => 'الجنوب'],
    ];
}

function insertLesson(array $data): int {
    $pdo = db();
    $stmt = $pdo->prepare('INSERT INTO lessons(filename, stored_name, subject, level, track, stage, week, branch, size, uploaded_at) VALUES(?,?,?,?,?,?,?,?,?,datetime("now"))');
    $stmt->execute([
        $data['filename'], $data['stored_name'], $data['subject'], (int)$data['level'], (int)$data['track'], (int)$data['stage'], (int)$data['week'], $data['branch'], (int)$data['size']
    ]);
    return (int)$pdo->lastInsertId();
}

function findLessons(array $filters, string $q = ''): array {
    $pdo = db();
    $where = [];
    $params = [];
    // حذف المادة من الفلاتر، وإضافة الفرع
    foreach ([['level','level'],['track','track'],['stage','stage'],['week','week'],['branch','branch']] as $pair) {
        [$key, $col] = $pair;
        if (isset($filters[$key]) && $filters[$key] !== '' && $filters[$key] !== 'اختر') {
            $where[] = "$col = ?";
            $params[] = $filters[$key];
        }
    }
    if ($q !== '') { $where[] = 'filename LIKE ?'; $params[] = '%' . $q . '%'; }
    $sql = 'SELECT * FROM lessons';
    if ($where) { $sql .= ' WHERE ' . implode(' AND ', $where); }
    $sql .= ' ORDER BY uploaded_at DESC';
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function findLessonByFilename(string $name): ?array {
    $pdo = db();
    $stmt = $pdo->prepare('SELECT * FROM lessons WHERE filename = ? OR stored_name = ? ORDER BY uploaded_at DESC LIMIT 1');
    $stmt->execute([$name, $name]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row ?: null;
}

function findLessonById(int $id): ?array {
    $pdo = db();
    $stmt = $pdo->prepare('SELECT * FROM lessons WHERE id = ?');
    $stmt->execute([$id]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row ?: null;
}

function updateLessonById(int $id, array $data): void {
    $pdo = db();
    $stmt = $pdo->prepare('UPDATE lessons SET filename = ?, stored_name = ?, level = ?, track = ?, stage = ?, week = ?, branch = ?, size = ?, uploaded_at = datetime("now") WHERE id = ?');
    $stmt->execute([
        $data['filename'], $data['stored_name'], (int)$data['level'], (int)$data['track'], (int)$data['stage'], (int)$data['week'], $data['branch'], (int)$data['size'], $id
    ]);
}

function deleteLessonById(int $id): void {
  $pdo = db();
  $stmt = $pdo->prepare('DELETE FROM lessons WHERE id = ?');
  $stmt->execute([$id]);
}

function humanSize(int $bytes): string {
    $units = ['B','KB','MB','GB'];
    $i = 0; $size = $bytes;
    while ($size >= 1024 && $i < count($units)-1) { $size /= 1024; $i++; }
    return number_format($size, 2) . ' ' . $units[$i];
}

function parseFilenameMeta(string $name): array {
    $meta = [];
    // الفرع
    if (preg_match('/(?:^|[_\-\s])centre(?=[_\-\s]|$)/i', $name)) {
        $meta['branch'] = 'center';
    } elseif (preg_match('/(?:^|[_\-\s])nord(?=[_\-\s]|$)/i', $name)) {
        $meta['branch'] = 'north';
    } elseif (preg_match('/(?:^|[_\-\s])sud(?=[_\-\s]|$)/i', $name)) {
        $meta['branch'] = 'south';
    }
    // المسار Par#
    if (preg_match('/(?:^|[_\-\s])par\s*(\d+)(?=[_\-\s]|$)/i', $name, $m)) {
        $t = (int)$m[1];
        $meta['track'] = max(min($t, max(tracks())), min(tracks()));
    }
    // المرحلة P# (تجنب P من PPTX بسبب الحدود)
    if (preg_match('/(?:^|[_\-\s])p\s*(\d+)(?=[_\-\s]|$)/i', $name, $m)) {
        $s = (int)$m[1];
        $meta['stage'] = max(min($s, max(stages())), min(stages()));
    }
    // الأسبوع SEM#
    if (preg_match('/(?:^|[_\-\s])sem\s*(\d+)(?=[_\-\s]|$)/i', $name, $m)) {
        $w = (int)$m[1];
        $meta['week'] = max(min($w, max(weeks())), min(weeks()));
    }
    // المستوى Level/Niv/Lvl
    if (preg_match('/(?:^|[_\-\s])(?:lvl|level|niv)\s*(\d+)(?=[_\-\s]|$)/i', $name, $m)) {
        $lv = (int)$m[1];
        $meta['level'] = max(min($lv, max(levels())), min(levels()));
    }
    return $meta;
}

?>