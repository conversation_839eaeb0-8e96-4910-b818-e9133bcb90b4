<?php
if (session_status() !== PHP_SESSION_ACTIVE) { session_start(); }

function admin_password_provider(): array {
    $file = __DIR__ . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'admin.pass';
    if (is_file($file)) { return ['type' => 'hash', 'value' => trim((string)@file_get_contents($file))]; }
    $envHash = getenv('ADMIN_PASSWORD_HASH');
    if ($envHash) { return ['type' => 'hash', 'value' => $envHash]; }
    $envPlain = getenv('ADMIN_PASSWORD');
    if ($envPlain) { return ['type' => 'plain', 'value' => $envPlain]; }
    return ['type' => 'none', 'value' => null];
}

function verify_admin_password(string $input): bool {
    $p = admin_password_provider();
    if ($p['type'] === 'hash') { return password_verify($input, $p['value']); }
    if ($p['type'] === 'plain') { return hash_equals($p['value'], $input); }
    return false;
}

function set_admin_password(string $plain): bool {
    $hash = password_hash($plain, PASSWORD_BCRYPT);
    $file = __DIR__ . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'admin.pass';
    $dir = dirname($file);
    if (!is_dir($dir)) { @mkdir($dir, 0777, true); }
    return (bool)@file_put_contents($file, $hash);
}

function admin_logged_in(): bool { return !empty($_SESSION['admin_logged_in']); }
function admin_login(): void { $_SESSION['admin_logged_in'] = true; }
function admin_logout(): void { unset($_SESSION['admin_logged_in']); }

?>
