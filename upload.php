<?php
require_once __DIR__ . '/db.php';

if (session_status() !== PHP_SESSION_ACTIVE) { session_start(); }

if (empty($_SESSION['csrf'])) { $_SESSION['csrf'] = bin2hex(random_bytes(16)); }

function sanitize_upload_name(string $name): string {
    $base = pathinfo($name, PATHINFO_BASENAME);
    $san = preg_replace('/[^A-Za-z0-9._\-\s]/u', '_', $base);
    $san = ltrim($san, '.');
    return $san === '' ? ('file_' . date('Ymd_His') . '.pptx') : $san;
}

function is_valid_ppt_mime(string $tmp): bool {
    $fi = finfo_open(FILEINFO_MIME_TYPE);
    $mime = $fi ? finfo_file($fi, $tmp) : '';
    if ($fi) { finfo_close($fi); }
    return in_array($mime, ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'], true);
}

$errors = [];
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfOk = isset($_POST['csrf_token']) && hash_equals($_SESSION['csrf'] ?? '', $_POST['csrf_token']);
    if (!$csrfOk) {
        $errors[] = 'طلب غير صالح: رمز CSRF مفقود أو غير صحيح.';
    } else {
        $level = $_POST['level'] ?? '';
        $track = $_POST['track'] ?? '';
        $stage = $_POST['stage'] ?? '';
        $week = $_POST['week'] ?? '';
        $branch = $_POST['branch'] ?? 'center';
        
        if (!isset($_FILES['ppt']) || $_FILES['ppt']['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'يرجى اختيار ملف PPTX صالح.';
        } else {
            $file = $_FILES['ppt'];
            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($ext, ['ppt', 'pptx'])) {
                $errors[] = 'يجب أن يكون الملف بصيغة PPT أو PPTX.';
            }
            if ($file['size'] > 104857600) { $errors[] = 'يتجاوز الملف الحد المسموح (100MB).'; }
            if (!is_valid_ppt_mime($file['tmp_name'])) { $errors[] = 'نوع الملف غير صالح بحسب MIME.'; }
        }

        if (!$errors) {
            ensureDataDirs();
            $uploadsDir = __DIR__ . DIRECTORY_SEPARATOR . 'uploads';
            $originalName = sanitize_upload_name($file['name']);
            $meta = parseFilenameMeta($originalName);
            if (isset($meta['branch'])) { $branch = $meta['branch']; }
            if (isset($meta['track'])) { $track = $meta['track']; }
            if (isset($meta['stage'])) { $stage = $meta['stage']; }
            if (isset($meta['week'])) { $week = $meta['week']; }
            if (isset($meta['level'])) { $level = $meta['level']; }

            $stored = $originalName;
            $dest = $uploadsDir . DIRECTORY_SEPARATOR . $stored;
            if (is_file($dest) || findLessonByFilename($stored)) {
                $base = pathinfo($originalName, PATHINFO_FILENAME);
                $ext2 = pathinfo($originalName, PATHINFO_EXTENSION);
                $suffix = date('Ymd_His');
                $stored = $base . '_' . $suffix . '.' . $ext2;
                $dest = $uploadsDir . DIRECTORY_SEPARATOR . $stored;
            }

            if (!move_uploaded_file($file['tmp_name'], $dest)) {
                $errors[] = 'فشل نقل الملف إلى المجلد.';
            } else {
                $id = insertLesson([
                    'filename' => $originalName,
                    'stored_name' => $stored,
                    'subject' => 'AR',
                    'level' => $level,
                    'track' => $track,
                    'stage' => $stage,
                    'week' => $week,
                    'branch' => $branch,
                    'size' => $file['size'],
                ]);
                $success = 'تم رفع الملف بنجاح (رقم #' . $id . ').';
            }
        }
    }
}

?><!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>رفع ملف - دروس الأمازيغية</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body class="container py-4">
  <header class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h4 m-0">رفع ملف</h1>
    <a class="btn btn-outline-secondary" href="index.php">عودة إلى القائمة</a>
  </header>

  <?php if ($errors): ?>
    <div class="alert alert-danger"><ul class="m-0"><?php foreach ($errors as $e): ?><li><?= htmlspecialchars($e) ?></li><?php endforeach; ?></ul></div>
  <?php endif; ?>
  <?php if ($success): ?>
    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
  <?php endif; ?>

  <form method="post" enctype="multipart/form-data" class="row g-3">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf'] ?? '') ?>">
    <div class="col-12 col-md-3">
      <label class="form-label">المستوى / المسار</label>
      <div class="input-group">
        <select name="level" class="form-select" required>
          <?php foreach (levels() as $lv): ?>
            <option value="<?= $lv ?>"><?= $lv ?></option>
          <?php endforeach; ?>
        </select>
        <select name="track" class="form-select" required>
          <?php foreach (tracks() as $tr): ?>
            <option value="<?= $tr ?>"><?= $tr ?></option>
          <?php endforeach; ?>
        </select>
      </div>
    </div>
    <div class="col-6 col-md-3">
      <label class="form-label">المرحلة</label>
      <select name="stage" class="form-select" required>
        <?php foreach (stages() as $st): ?>
          <option value="<?= $st ?>"><?= $st ?></option>
        <?php endforeach; ?>
      </select>
    </div>
    <div class="col-6 col-md-3">
      <label class="form-label">الأسبوع</label>
      <select name="week" class="form-select" required>
        <?php foreach (weeks() as $wk): ?>
          <option value="<?= $wk ?>"><?= $wk ?></option>
        <?php endforeach; ?>
      </select>
    </div>
    <div class="col-12 col-md-3">
      <label class="form-label">الفرع</label>
      <select name="branch" class="form-select" required>
        <?php foreach (branches() as $br): ?>
          <option value="<?= htmlspecialchars($br['code']) ?>"><?= htmlspecialchars($br['name']) ?></option>
        <?php endforeach; ?>
      </select>
    </div>

    <div class="col-12">
      <label class="form-label">ملف الدرس (PPT/PPTX)</label>
      <input type="file" name="ppt" class="form-control" accept=".ppt,.pptx" required>
    </div>

    <div class="col-12">
      <button class="btn btn-primary" type="submit">رفع</button>
    </div>
  </form>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
