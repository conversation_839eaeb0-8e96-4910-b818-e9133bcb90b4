<?php
require_once __DIR__ . '/db.php';

function csrf_token(): string {
    if (session_status() !== PHP_SESSION_ACTIVE) { session_start(); }
    if (empty($_SESSION['csrf'])) { $_SESSION['csrf'] = bin2hex(random_bytes(16)); }
    return $_SESSION['csrf'];
}

$filters = [
    // حذف المادة، واعتماد الفرع
    'level' => $_GET['level'] ?? '1',
    'track' => $_GET['track'] ?? '1',
    'stage' => $_GET['stage'] ?? '1',
    'week' => $_GET['week'] ?? '1',
    'branch' => $_GET['branch'] ?? 'center',
];
$q = trim($_GET['q'] ?? '');
$tab = $_GET['tab'] ?? 'downloads';
$lessons = findLessons($filters, $q);
?>
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>إدارة الملفات - دروس الأمازيغية</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
  <style>
    :root { --brand: #6c2bd9; --brand-soft:#ede7ff; }
    body { background: #f7f9fc; }
    .brand { color: var(--brand); }
    .btn-brand { background: var(--brand); color:#fff; }
    .btn-brand:hover{ background:#5822b3; color:#fff; }
    .sidebar { background:#f4f6fb; border:1px solid #e6e9f2; border-radius:12px; }
    .sidebar .item { display:flex; align-items:center; justify-content:space-between; padding:12px 16px; border-radius:10px; cursor:pointer; }
    .sidebar .item.active { background: var(--brand-soft); border:1px solid #d9cdfa; }
    .card-hover:hover { border-color: var(--brand); box-shadow: 0 0.75rem 1.25rem rgba(108,43,217,.12); }
    .doc-icon { width:40px; height:40px; border-radius:12px; background:#ffe7e7; display:flex; align-items:center; justify-content:center; }
    .doc-icon svg{ width:20px; height:20px; color:#dc3545; }
  </style>
</head>
<body class="container-fluid py-4">
  <div class="row g-4">
    <!-- المحتوى الرئيسي -->
    <div class="col-12">


      <!-- شريط الفلاتر -->
      <section class="mb-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
              <div class="col-6 col-md-2">
                <label class="form-label">الأسبوع</label>
                <select name="week" class="form-select">
                  <?php foreach (weeks() as $wk): ?>
                    <option value="<?= $wk ?>" <?= $filters['week']==$wk?'selected':'' ?>><?= $wk ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              <div class="col-6 col-md-2">
                <label class="form-label">المرحلة</label>
                <select name="stage" class="form-select">
                  <?php foreach (stages() as $st): ?>
                    <option value="<?= $st ?>" <?= $filters['stage']==$st?'selected':'' ?>><?= $st ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              <div class="col-12 col-md-3">
                <label class="form-label">المستوى / المسار</label>
                <div class="input-group">
                  <select name="level" class="form-select">
                    <?php foreach (levels() as $lv): ?>
                      <option value="<?= $lv ?>" <?= $filters['level']==$lv?'selected':'' ?>><?= $lv ?></option>
                    <?php endforeach; ?>
                  </select>
                  <select name="track" class="form-select">
                    <?php foreach (tracks() as $tr): ?>
                      <option value="<?= $tr ?>" <?= $filters['track']===$tr?'selected':'' ?>><?= $tr ?></option>
                    <?php endforeach; ?>
                  </select>
                </div>
              </div>
              <div class="col-12 col-md-3">
                <label class="form-label">الفرع</label>
                <select name="branch" class="form-select">
                  <?php foreach (branches() as $br): ?>
                    <option value="<?= htmlspecialchars($br['code']) ?>" <?= $filters['branch']===$br['code']?'selected':'' ?>><?= htmlspecialchars($br['name']) ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              <div class="col-12 col-md-2 d-flex gap-2">
                <button class="btn btn-brand flex-fill" type="submit">بحث</button>
                <a class="btn btn-outline-secondary flex-fill" href="index.php">مسح</a>
              </div>
              <div class="col-12">
                <div class="input-group">
                  <span class="input-group-text">🔎</span>
                  <input class="form-control" type="text" name="q" value="<?= htmlspecialchars($q) ?>" placeholder="تصفية النتائج حسب اسم الملف">
                </div>
              </div>
              <input type="hidden" name="tab" value="downloads" />
            </form>
            <div class="small text-muted mt-2">الإصدار الحالي: v1.0 · الإصدار الأحدث المدعوم: v1.0.9</div>
          </div>
        </div>
      </section>

      <!-- النتائج -->
      <section>
        <div class="d-flex align-items-center gap-2 mb-2">
          <h2 class="h5 m-0">ملفات للتحميل</h2>
          <span class="badge bg-secondary">نتائج <?= count($lessons) ?></span>
        </div>
        <?php if (!$lessons): ?>
          <div class="alert alert-light border">لا توجد نتائج</div>
        <?php else: ?>
          <div class="vstack gap-3">
            <?php foreach ($lessons as $item): ?>
              <div class="card card-hover">
                <div class="card-body d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center gap-3">
                    <button class="btn btn-brand">تنزيل</button>
                    <div>
                      <div class="fw-semibold"><?= htmlspecialchars($item['filename']) ?></div>
                      <div class="text-muted small">المستوى <?= (int)$item['level'] ?> · المسار <?= (int)$item['track'] ?> · المرحلة <?= (int)$item['stage'] ?> · الأسبوع <?= (int)$item['week'] ?> · الفرع <?= htmlspecialchars($item['branch'] ?? '') ?> · الحجم <?= humanSize((int)$item['size']) ?></div>
                    </div>
                  </div>
                  <a class="doc-icon text-decoration-none" href="download.php?id=<?= (int)$item['id'] ?>" title="تنزيل">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V5.5L9.5 0H4z"/>
                      <path d="M9.5 0v4a1 1 0 0 0 1 1H14"/>
                    </svg>
                  </a>
                </div>
              </div>
            <?php endforeach; ?>
          </div>
        <?php endif; ?>
      </section>
    </div>


  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>