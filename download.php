<?php
require_once __DIR__ . '/db.php';

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$lesson = $id ? findLessonById($id) : null;
if (!$lesson) {
    http_response_code(404);
    echo 'الملف غير موجود';
    exit;
}

$path = __DIR__ . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . $lesson['stored_name'];
if (!is_file($path)) {
    http_response_code(410);
    echo 'الملف غير متاح على الخادم';
    exit;
}

$filename = $lesson['filename'];
$mime = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
if (strtolower(pathinfo($filename, PATHINFO_EXTENSION)) === 'ppt') {
    $mime = 'application/vnd.ms-powerpoint';
}

header('Content-Description: File Transfer');
header('Content-Type: ' . $mime);
header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
header('Content-Length: ' . filesize($path));
readfile($path);
exit;